﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Hsse.Data.Entities;

public partial class MasterDBContext : DbContext
{
    public MasterDBContext()
    {
    }

    public MasterDBContext(DbContextOptions<MasterDBContext> options)
        : base(options)
    {
    }

    public virtual DbSet<MstActionParty> MstActionParties { get; set; }

    public virtual DbSet<MstActionPartyUserMapping> MstActionPartyUserMappings { get; set; }

    public virtual DbSet<MstAnnouncement> MstAnnouncements { get; set; }

    public virtual DbSet<MstAnnouncementCategory> MstAnnouncementCategories { get; set; }

    public virtual DbSet<MstAnnouncementDocument> MstAnnouncementDocuments { get; set; }

    public virtual DbSet<MstAnnouncementReceiver> MstAnnouncementReceivers { get; set; }

    public virtual DbSet<MstApiLog> MstApiLogs { get; set; }

    public virtual DbSet<MstAuditLog> MstAuditLogs { get; set; }

    public virtual DbSet<MstCommentLike> MstCommentLikes { get; set; }

    public virtual DbSet<MstDeviceDetail> MstDeviceDetails { get; set; }

    public virtual DbSet<MstDocumentLibrary> MstDocumentLibraries { get; set; }

    public virtual DbSet<MstEvent> MstEvents { get; set; }

    public virtual DbSet<MstEventResponse> MstEventResponses { get; set; }

    public virtual DbSet<MstFacility> MstFacilities { get; set; }

    public virtual DbSet<MstFeedback> MstFeedbacks { get; set; }

    public virtual DbSet<MstFollowupPost> MstFollowupPosts { get; set; }

    public virtual DbSet<MstGroup> MstGroups { get; set; }

    public virtual DbSet<MstGroupMember> MstGroupMembers { get; set; }

    public virtual DbSet<MstInspection> MstInspections { get; set; }

    public virtual DbSet<MstInspectionCategory> MstInspectionCategories { get; set; }

    public virtual DbSet<MstInspectionItem> MstInspectionItems { get; set; }

    public virtual DbSet<MstInspectionItemAudit> MstInspectionItemAudits { get; set; }

    public virtual DbSet<MstInspectionItemComment> MstInspectionItemComments { get; set; }

    public virtual DbSet<MstLikesConfig> MstLikesConfigs { get; set; }

    public virtual DbSet<MstNewsletter> MstNewsletters { get; set; }

    public virtual DbSet<MstNotification> MstNotifications { get; set; }

    public virtual DbSet<MstOrganisation> MstOrganisations { get; set; }

    public virtual DbSet<MstOtpValidation> MstOtpValidations { get; set; }

    public virtual DbSet<MstPermission> MstPermissions { get; set; }

    public virtual DbSet<MstPost> MstPosts { get; set; }

    public virtual DbSet<MstPostCategory> MstPostCategories { get; set; }

    public virtual DbSet<MstPostComment> MstPostComments { get; set; }

    public virtual DbSet<MstPostMedium> MstPostMedia { get; set; }

    public virtual DbSet<MstPostStatus> MstPostStatuses { get; set; }

    public virtual DbSet<MstRoleMenuPermission> MstRoleMenuPermissions { get; set; }

    public virtual DbSet<MstUser> MstUsers { get; set; }

    public virtual DbSet<MstUserFacilityMapping> MstUserFacilityMappings { get; set; }

    public virtual DbSet<MstUserRolesConfig> MstUserRolesConfigs { get; set; }

    public virtual DbSet<MstUsersRole> MstUsersRoles { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Server=************;Initial Catalog=HSSE_DB_Latest;User Id=sa;Password=**$q2023P@s;Trusted_Connection=True;TrustServerCertificate=True;MultipleActiveResultSets=true;Integrated Security=false;");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<MstActionParty>(entity =>
        {
            entity.HasKey(e => e.ActionPartyId).HasName("PK__mstActio__D1F41D4A45273F5E");

            entity.ToTable("mstActionParty");

            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.ModifiedAt)
                .HasColumnType("datetime")
                .HasColumnName("modified_at");
            entity.Property(e => e.ModifiedBy).HasColumnName("modified_by");
            entity.Property(e => e.Name)
                .HasMaxLength(255)
                .HasColumnName("name");
            entity.Property(e => e.Observation).HasMaxLength(500);
            entity.Property(e => e.ObservationMediaUrl).HasMaxLength(500);
            entity.Property(e => e.RecommendationMediaUrl).HasMaxLength(500);

            entity.HasOne(d => d.Facility).WithMany(p => p.MstActionParties)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK_FacilityID_1233212");
        });

        modelBuilder.Entity<MstActionPartyUserMapping>(entity =>
        {
            entity.HasKey(e => e.MappingId).HasName("PK__mstActio__F8A9097E7DD1F45A");

            entity.ToTable("mstActionPartyUserMapping");

            entity.Property(e => e.MappingId).HasColumnName("mappingId");
            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.ModifiedAt)
                .HasColumnType("datetime")
                .HasColumnName("modified_at");
            entity.Property(e => e.ModifiedBy).HasColumnName("modified_by");

            entity.HasOne(d => d.ActionParty).WithMany(p => p.MstActionPartyUserMappings)
                .HasForeignKey(d => d.ActionPartyId)
                .HasConstraintName("FK_ActionPartyId_1233212");

            entity.HasOne(d => d.User).WithMany(p => p.MstActionPartyUserMappings)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_UserId_123495559212");
        });

        modelBuilder.Entity<MstAnnouncement>(entity =>
        {
            entity.HasKey(e => e.AnnouncementsId).HasName("PK__mstAnnou__73FE1197FE284125");

            entity.ToTable("mstAnnouncements");

            entity.Property(e => e.AnnouncementsId).HasColumnName("Announcements_id");
            entity.Property(e => e.AnnouncementDocument).HasMaxLength(500);
            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasColumnName("created_at");
            entity.Property(e => e.CreatedBy).HasColumnName("created_by");
            entity.Property(e => e.Description)
                .HasColumnType("text")
                .HasColumnName("description");
            entity.Property(e => e.ExpiryAt).HasColumnType("datetime");
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.ModifiedAt)
                .HasColumnType("datetime")
                .HasColumnName("modified_at");
            entity.Property(e => e.ModifiedBy).HasColumnName("modified_by");
            entity.Property(e => e.ScheduleAt).HasColumnType("datetime");
            entity.Property(e => e.Status).HasColumnName("status");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");

            entity.HasOne(d => d.Category).WithMany(p => p.MstAnnouncements)
                .HasForeignKey(d => d.CategoryId)
                .HasConstraintName("FK_AnnoucementCategoryId");

            entity.HasOne(d => d.Facility).WithMany(p => p.MstAnnouncements)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK_FacilityID_123212");
        });

        modelBuilder.Entity<MstAnnouncementCategory>(entity =>
        {
            entity.HasKey(e => e.AnnoucementCategoryId).HasName("PK_MstAnnoucementCategory");

            entity.ToTable("MstAnnouncementCategory");
        });

        modelBuilder.Entity<MstAnnouncementDocument>(entity =>
        {
            entity.HasKey(e => e.DocumentId).HasName("PK__mstAnnou__1ABEEF6FA6F67628");

            entity.ToTable("mstAnnouncementDocuments");

            entity.Property(e => e.DocumentId).HasColumnName("DocumentID");
            entity.Property(e => e.AnnouncementId).HasColumnName("AnnouncementID");
            entity.Property(e => e.DocumentFile).HasMaxLength(1);
            entity.Property(e => e.DocumentName).HasMaxLength(1);

            entity.HasOne(d => d.Announcement).WithMany(p => p.MstAnnouncementDocuments)
                .HasForeignKey(d => d.AnnouncementId)
                .HasConstraintName("FK__mstAnnoun__Annou__18EBB532");
        });

        modelBuilder.Entity<MstAnnouncementReceiver>(entity =>
        {
            entity.HasKey(e => e.ReceiverId).HasName("PK__mstAnnou__FEBB5F07158666C3");

            entity.ToTable("mstAnnouncementReceivers");

            entity.Property(e => e.ReceiverId).HasColumnName("ReceiverID");
            entity.Property(e => e.AnnouncementId).HasColumnName("AnnouncementID");
            entity.Property(e => e.Delivered).HasDefaultValue(false);
            entity.Property(e => e.EventId).HasColumnName("EventID");
            entity.Property(e => e.GroupId).HasColumnName("GroupID");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Announcement).WithMany(p => p.MstAnnouncementReceivers)
                .HasForeignKey(d => d.AnnouncementId)
                .HasConstraintName("FK__mstAnnoun__Annou__02FC7413");

            entity.HasOne(d => d.Event).WithMany(p => p.MstAnnouncementReceivers)
                .HasForeignKey(d => d.EventId)
                .HasConstraintName("FK_EventID_987678888887");

            entity.HasOne(d => d.Group).WithMany(p => p.MstAnnouncementReceivers)
                .HasForeignKey(d => d.GroupId)
                .HasConstraintName("FK__mstAnnoun__Group__04E4BC85");

            entity.HasOne(d => d.User).WithMany(p => p.MstAnnouncementReceivers)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK__mstAnnoun__UserI__03F0984C");
        });

        modelBuilder.Entity<MstApiLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__mstApiLo__3214EC073684F102");

            entity.ToTable("mstApiLogs");

            entity.Property(e => e.CreatedOn).HasColumnType("datetime");
            entity.Property(e => e.Level).HasMaxLength(10);
            entity.Property(e => e.Logger).HasMaxLength(255);
            entity.Property(e => e.Url).HasMaxLength(255);
        });

        modelBuilder.Entity<MstAuditLog>(entity =>
        {
            entity.HasKey(e => e.AuditLogId).HasName("PK__mstAudit__EB5F6CDD91C94153");

            entity.ToTable("mstAuditLogs");

            entity.Property(e => e.AuditLogId).HasColumnName("AuditLogID");
            entity.Property(e => e.ChangedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.NewValues).HasColumnType("text");
            entity.Property(e => e.OldValues).HasColumnType("text");
            entity.Property(e => e.OperationType)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.TableName)
                .HasMaxLength(255)
                .IsUnicode(false);
        });

        modelBuilder.Entity<MstCommentLike>(entity =>
        {
            entity.HasKey(e => e.CommentLikeId).HasName("PK__mstComme__D36E159DE0EC9564");

            entity.ToTable("mstCommentLikes");

            entity.Property(e => e.CommentLikeId).HasColumnName("CommentLikeID");
            entity.Property(e => e.CommentId).HasColumnName("CommentID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Comment).WithMany(p => p.MstCommentLikes)
                .HasForeignKey(d => d.CommentId)
                .HasConstraintName("FK_CommentID_1234349934495559212");

            entity.HasOne(d => d.User).WithMany(p => p.MstCommentLikes)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK_UserID_1234349934495559212");
        });

        modelBuilder.Entity<MstDeviceDetail>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__mstDevic__3214EC07CBBC54C3");

            entity.ToTable("mstDeviceDetails");

            entity.Property(e => e.AppVersion)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.Datetime).HasColumnType("datetime");
            entity.Property(e => e.DeviceUniqueId)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("DeviceUniqueID");
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.Fcm)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("FCM");
            entity.Property(e => e.Lat).HasMaxLength(500);
            entity.Property(e => e.Location)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.Long).HasMaxLength(500);
            entity.Property(e => e.Osname)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("OSName");

            entity.HasOne(d => d.Facility).WithMany(p => p.MstDeviceDetails)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK__mstDevice__Facil__06CD04F7");
        });

        modelBuilder.Entity<MstDocumentLibrary>(entity =>
        {
            entity.HasKey(e => e.DocumentId).HasName("PK__mstDocum__1ABEEF0FA1ECB684");

            entity.ToTable("mstDocumentLibrary");

            entity.Property(e => e.Category)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.Date).HasColumnType("datetime");
            entity.Property(e => e.DocumentUrl)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.Title)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.Version)
                .HasMaxLength(500)
                .IsUnicode(false);
        });

        modelBuilder.Entity<MstEvent>(entity =>
        {
            entity.HasKey(e => e.EventId).HasName("PK__mstEvent__7944C8704BACE7A1");

            entity.ToTable("mstEvents");

            entity.Property(e => e.EventId).HasColumnName("EventID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Description).HasColumnType("text");
            entity.Property(e => e.EventDateTime).HasColumnType("datetime");
            entity.Property(e => e.ExpiryAt).HasColumnType("datetime");
            entity.Property(e => e.ExternalLink).HasMaxLength(512);
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.Location).HasMaxLength(255);
            entity.Property(e => e.MediaUrl)
                .HasMaxLength(512)
                .IsUnicode(false)
                .HasColumnName("MediaURL");
            entity.Property(e => e.ScheduleAt).HasColumnType("datetime");
            entity.Property(e => e.Title).HasMaxLength(255);

            entity.HasOne(d => d.Facility).WithMany(p => p.MstEvents)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK_FacilityID_123495559212");
        });

        modelBuilder.Entity<MstEventResponse>(entity =>
        {
            entity.HasKey(e => e.ResponseId).HasName("PK__mstEvent__1AAA640CB5411E4F");

            entity.ToTable("mstEventResponses");

            entity.Property(e => e.ResponseId).HasColumnName("ResponseID");
            entity.Property(e => e.EventId).HasColumnName("EventID");
            entity.Property(e => e.RespondedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Event).WithMany(p => p.MstEventResponses)
                .HasForeignKey(d => d.EventId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstEventR__Event__7D439ABD");

            entity.HasOne(d => d.User).WithMany(p => p.MstEventResponses)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstEventR__UserI__7E37BEF6");
        });

        modelBuilder.Entity<MstFacility>(entity =>
        {
            entity.HasKey(e => e.FacilityId).HasName("PK__mstFacil__5FB08B94F219D64F");

            entity.ToTable("mstFacilities");

            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.FacilityCode).HasMaxLength(512);
            entity.Property(e => e.FacilityName).HasMaxLength(512);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.OrgId).HasColumnName("OrgID");

            entity.HasOne(d => d.Org).WithMany(p => p.MstFacilities)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstFacili__OrgID__778AC167");
        });

        modelBuilder.Entity<MstFeedback>(entity =>
        {
            entity.HasKey(e => e.FeedbackId).HasName("PK__mstFeedb__6A4BEDF6F682CD78");

            entity.ToTable("mstFeedback");

            entity.Property(e => e.FeedbackId).HasColumnName("FeedbackID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Date).HasColumnType("datetime");
            entity.Property(e => e.Description).IsUnicode(false);
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.FilePath)
                .IsUnicode(false)
                .HasColumnName("filePath");
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Response).IsUnicode(false);
            entity.Property(e => e.Title).HasMaxLength(255);

            entity.HasOne(d => d.Facility).WithMany(p => p.MstFeedbacks)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK_FacilityID_1234349934495559212");
        });

        modelBuilder.Entity<MstFollowupPost>(entity =>
        {
            entity.HasKey(e => e.FollowupId).HasName("PK__mstFollo__C63562713A45613F");

            entity.ToTable("mstFollowupPosts");

            entity.Property(e => e.FollowupId).HasColumnName("FollowupID");
            entity.Property(e => e.ArchivedTime).HasColumnType("datetime");
            entity.Property(e => e.AssignedTime).HasColumnType("datetime");
            entity.Property(e => e.AssignedTo).HasMaxLength(500);
            entity.Property(e => e.Comments).HasMaxLength(500);
            entity.Property(e => e.CompletedTime).HasColumnType("datetime");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.FollowedupTime).HasColumnType("datetime");
            entity.Property(e => e.Format).HasMaxLength(100);
            entity.Property(e => e.PostId).HasColumnName("PostID");

            entity.HasOne(d => d.Post).WithMany(p => p.MstFollowupPosts)
                .HasForeignKey(d => d.PostId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstFollow__PostI__6A30C649");
        });

        modelBuilder.Entity<MstGroup>(entity =>
        {
            entity.HasKey(e => e.GroupId).HasName("PK__mstGroup__149AF30AECED4B58");

            entity.ToTable("mstGroups");

            entity.Property(e => e.GroupId).HasColumnName("GroupID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.GroupName).HasMaxLength(255);
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");

            entity.HasOne(d => d.CreatedByNavigation).WithMany(p => p.MstGroups)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstGroups__Creat__7F2BE32F");
        });

        modelBuilder.Entity<MstGroupMember>(entity =>
        {
            entity.HasKey(e => e.GroupMemberId).HasName("PK__mstGroup__344812B25AB93399");

            entity.ToTable("mstGroupMembers");

            entity.Property(e => e.GroupMemberId).HasColumnName("GroupMemberID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.GroupId).HasColumnName("GroupID");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Group).WithMany(p => p.MstGroupMembers)
                .HasForeignKey(d => d.GroupId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstGroupM__Group__01142BA1");

            entity.HasOne(d => d.User).WithMany(p => p.MstGroupMembers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstGroupM__UserI__02084FDA");
        });

        modelBuilder.Entity<MstInspection>(entity =>
        {
            entity.HasKey(e => e.InspectionId).HasName("PK__mstInspe__30B2DC08D206BBB0");

            entity.ToTable("mstInspections");

            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.Description).HasColumnType("text");
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.InspectionDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.ReferenceNo).HasMaxLength(500);
            entity.Property(e => e.Title).HasMaxLength(500);

            entity.HasOne(d => d.Facility).WithMany(p => p.MstInspections)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK__mstInspec__Facil__7C4F7684");
        });

        modelBuilder.Entity<MstInspectionCategory>(entity =>
        {
            entity.HasKey(e => e.InspectionCategoryId).HasName("PK_MstInspectionCategory");

            entity.ToTable("mstInspectionCategory");
        });

        modelBuilder.Entity<MstInspectionItem>(entity =>
        {
            entity.HasKey(e => e.ItemId).HasName("PK__mstInspe__727E838B9A28997E");

            entity.ToTable("mstInspectionItems");

            entity.Property(e => e.ActionPartyId)
                .HasMaxLength(500)
                .HasColumnName("ActionPartyID");
            entity.Property(e => e.AfterImagePath)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.CompletionDateTime).HasColumnType("datetime");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.Description)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.Location).HasMaxLength(500);
            entity.Property(e => e.ModifiedAt).HasColumnType("datetime");
            entity.Property(e => e.ObservationMediaUrl).HasMaxLength(500);
            entity.Property(e => e.RecommendationMediaUrl).HasMaxLength(500);
            entity.Property(e => e.SpecificLocation)
                .HasMaxLength(255)
                .IsUnicode(false);

            entity.HasOne(d => d.Inspection).WithMany(p => p.MstInspectionItems)
                .HasForeignKey(d => d.InspectionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstInspec__Inspe__6D0D32F4");
        });

        modelBuilder.Entity<MstInspectionItemAudit>(entity =>
        {
            entity.HasKey(e => e.AuditId).HasName("PK__mstInspe__A17F23987AF9E263");

            entity.ToTable("mstInspectionItemAudit");

            entity.Property(e => e.ChangedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.NewAfterImagePath)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.NewRectification).HasColumnType("text");
            entity.Property(e => e.NewStatus)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.OldAfterImagePath)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.OldRectification).HasColumnType("text");
            entity.Property(e => e.OldStatus)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.Item).WithMany(p => p.MstInspectionItemAudits)
                .HasForeignKey(d => d.ItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstInspec__ItemI__6EF57B66");
        });

        modelBuilder.Entity<MstInspectionItemComment>(entity =>
        {
            entity.HasKey(e => e.InspectionCommentId).HasName("PK__mstInspe__AF029C6195703D73");

            entity.ToTable("mstInspectionItemComments");

            entity.Property(e => e.CommentText).HasColumnType("text");
            entity.Property(e => e.CommentedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Item).WithMany(p => p.MstInspectionItemComments)
                .HasForeignKey(d => d.ItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstInspec__ItemI__6E01572D");
        });

        modelBuilder.Entity<MstLikesConfig>(entity =>
        {
            entity.HasKey(e => e.LikeId).HasName("PK__mstLikes__A2922CF4A26EA645");

            entity.ToTable("mstLikesConfig");

            entity.Property(e => e.LikeId).HasColumnName("LikeID");
            entity.Property(e => e.EventId).HasColumnName("EventID");
            entity.Property(e => e.LikedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.PostId).HasColumnName("PostID");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Event).WithMany(p => p.MstLikesConfigs)
                .HasForeignKey(d => d.EventId)
                .HasConstraintName("FK__mstLikesC__Event__7A672E12");

            entity.HasOne(d => d.Post).WithMany(p => p.MstLikesConfigs)
                .HasForeignKey(d => d.PostId)
                .HasConstraintName("FK__mstLikesC__PostI__71D1E811");

            entity.HasOne(d => d.User).WithMany(p => p.MstLikesConfigs)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstLikesC__UserI__72C60C4A");
        });

        modelBuilder.Entity<MstNewsletter>(entity =>
        {
            entity.HasKey(e => e.NewsletterId).HasName("PK__mstNewsl__34A1DE1D1BD0CC94");

            entity.ToTable("mstNewsletter");

            entity.Property(e => e.NewsletterId).HasColumnName("NewsletterID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Description).HasColumnType("text");
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ScheduleAt).HasColumnType("datetime");
            entity.Property(e => e.ThumbnailPath)
                .HasMaxLength(512)
                .IsUnicode(false);
            entity.Property(e => e.Title).HasMaxLength(255);

            entity.HasOne(d => d.Facility).WithMany(p => p.MstNewsletters)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK_FacilityID_12343434495559212");
        });

        modelBuilder.Entity<MstNotification>(entity =>
        {
            entity.HasKey(e => e.NotificationId).HasName("PK__mstNotif__20CF2E325A7C524A");

            entity.ToTable("mstNotifications");

            entity.Property(e => e.NotificationId).HasColumnName("NotificationID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Heading).HasMaxLength(255);
            entity.Property(e => e.IsRead).HasDefaultValue(false);
            entity.Property(e => e.Message).HasColumnType("text");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.User).WithMany(p => p.MstNotifications)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstNotifi__UserI__05D8E0BE");
        });

        modelBuilder.Entity<MstOrganisation>(entity =>
        {
            entity.HasKey(e => e.OrgId).HasName("PK__mstOrgan__420C9E0CF006F240");

            entity.ToTable("mstOrganisations");

            entity.Property(e => e.OrgId).HasColumnName("OrgID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.OrganisationName).HasMaxLength(512);
        });

        modelBuilder.Entity<MstOtpValidation>(entity =>
        {
            entity.ToTable("mstOtpValidation");

            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasColumnName("ID");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.HashData)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.UserId).HasColumnName("UserID");
        });

        modelBuilder.Entity<MstPermission>(entity =>
        {
            entity.HasKey(e => e.PermissionId).HasName("PK__mstPermi__EFA6FB0F7825D8A3");

            entity.ToTable("mstPermission");

            entity.Property(e => e.PermissionId).HasColumnName("PermissionID");
            entity.Property(e => e.ActionName).HasMaxLength(100);
            entity.Property(e => e.AreaName).HasMaxLength(100);
            entity.Property(e => e.ControllerName).HasMaxLength(100);
            entity.Property(e => e.Icon).HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.MenuName)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.OrderNo).HasDefaultValue(0);
            entity.Property(e => e.RouteParams).HasMaxLength(250);
        });

        modelBuilder.Entity<MstPost>(entity =>
        {
            entity.HasKey(e => e.PostId).HasName("PK__mstPosts__AA1260389CDD77E6");

            entity.ToTable("mstPosts");

            entity.Property(e => e.PostId).HasColumnName("PostID");
            entity.Property(e => e.ClosedDescription).HasColumnName("Closed_Description");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.Description).HasColumnType("text");
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.Location)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.PostType)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.RequiresFollowup)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Facility).WithMany(p => p.MstPosts)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK__mstPosts__Facili__7B5B524B");

            entity.HasOne(d => d.TaggedCategory).WithMany(p => p.MstPosts)
                .HasForeignKey(d => d.TaggedCategoryId)
                .HasConstraintName("FK__mstPosts__Tagged__76969D2E");

            entity.HasOne(d => d.User).WithMany(p => p.MstPosts)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstPosts__UserID__6FE99F9F");
        });

        modelBuilder.Entity<MstPostCategory>(entity =>
        {
            entity.HasKey(e => e.CatId).HasName("PK__mstPostC__6A1C8ADAADFA0979");

            entity.ToTable("mstPostCategories");

            entity.Property(e => e.CatId).HasColumnName("CatID");
            entity.Property(e => e.CategoryName).HasMaxLength(512);
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<MstPostComment>(entity =>
        {
            entity.HasKey(e => e.CommentId).HasName("PK__mstPostC__C3B4DFAAB420BF91");

            entity.ToTable("mstPostComments");

            entity.Property(e => e.CommentId).HasColumnName("CommentID");
            entity.Property(e => e.CommentText).HasColumnType("text");
            entity.Property(e => e.CommentedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.PostId).HasColumnName("PostID");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Post).WithMany(p => p.MstPostComments)
                .HasForeignKey(d => d.PostId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstPostCo__PostI__73BA3083");

            entity.HasOne(d => d.User).WithMany(p => p.MstPostComments)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstPostCo__UserI__74AE54BC");
        });

        modelBuilder.Entity<MstPostMedium>(entity =>
        {
            entity.HasKey(e => e.MediaId).HasName("PK__mstPostM__B2C2B5AF2A378238");

            entity.ToTable("mstPostMedia");

            entity.Property(e => e.MediaId).HasColumnName("MediaID");
            entity.Property(e => e.AfterMediaUrl)
                .HasMaxLength(512)
                .IsUnicode(false)
                .HasColumnName("AfterMediaURL");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.MediaUrl)
                .HasMaxLength(512)
                .IsUnicode(false)
                .HasColumnName("MediaURL");
            entity.Property(e => e.PostId).HasColumnName("PostID");

            entity.HasOne(d => d.Post).WithMany(p => p.MstPostMedia)
                .HasForeignKey(d => d.PostId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstPostMe__PostI__70DDC3D8");
        });

        modelBuilder.Entity<MstPostStatus>(entity =>
        {
            entity.HasKey(e => e.StatusId).HasName("PK__mstPostS__C8EE20437A1C8B0F");

            entity.ToTable("mstPostStatus");

            entity.Property(e => e.StatusId).HasColumnName("StatusID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.StatusName).HasMaxLength(512);
        });

        modelBuilder.Entity<MstRoleMenuPermission>(entity =>
        {
            entity.HasKey(e => e.RoleMenuPermissionsId).HasName("PK__mstRoleM__9E4EE4810DE1497E");

            entity.ToTable("mstRoleMenuPermissions");

            entity.Property(e => e.CanCreate).HasDefaultValue(false);
            entity.Property(e => e.CanDelete).HasDefaultValue(false);
            entity.Property(e => e.CanEdit).HasDefaultValue(false);
            entity.Property(e => e.CanView).HasDefaultValue(false);
            entity.Property(e => e.PermissionId).HasColumnName("PermissionID");

            entity.HasOne(d => d.Permission).WithMany(p => p.MstRoleMenuPermissions)
                .HasForeignKey(d => d.PermissionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PermissionID_129292");

            entity.HasOne(d => d.Role).WithMany(p => p.MstRoleMenuPermissions)
                .HasForeignKey(d => d.RoleId)
                .HasConstraintName("FK_RoleId_129292");
        });

        modelBuilder.Entity<MstUser>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("PK__mstUsers__1788CCAC7598E1F0");

            entity.ToTable("mstUsers");

            entity.Property(e => e.UserId).HasColumnName("UserID");
            entity.Property(e => e.Bio)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.ContactNumber)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.Email)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.EmployeeCode).HasMaxLength(500);
            entity.Property(e => e.FirstName)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.LastLogin).HasColumnType("datetime");
            entity.Property(e => e.LastName)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.PrimaryFacilityId).HasColumnName("PrimaryFacilityID");
            entity.Property(e => e.ProfileImageUrl)
                .HasMaxLength(512)
                .IsUnicode(false)
                .HasColumnName("ProfileImageURL");
            entity.Property(e => e.Username)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<MstUserFacilityMapping>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__mstUserF__3214EC2716D05E62");

            entity.ToTable("mstUserFacilityMapping");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("('CURRENT_TIMESTAMP')")
                .HasColumnType("datetime");
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Facility).WithMany(p => p.MstUserFacilityMappings)
                .HasForeignKey(d => d.FacilityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstUserFa__Facil__797309D9");

            entity.HasOne(d => d.User).WithMany(p => p.MstUserFacilityMappings)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__mstUserFa__UserI__787EE5A0");
        });

        modelBuilder.Entity<MstUserRolesConfig>(entity =>
        {
            entity.HasKey(e => e.UserRoleConfigId).HasName("PK__mstUserR__F90030B9BF9B563E");

            entity.ToTable("mstUserRolesConfig");

            entity.Property(e => e.UserRoleConfigId).HasColumnName("UserRoleConfigID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.FacilityId).HasColumnName("FacilityID");
            entity.Property(e => e.RoleId).HasColumnName("RoleID");
            entity.Property(e => e.UserId).HasColumnName("UserID");

            entity.HasOne(d => d.Facility).WithMany(p => p.MstUserRolesConfigs)
                .HasForeignKey(d => d.FacilityId)
                .HasConstraintName("FK_facilityId_11223");

            entity.HasOne(d => d.Role).WithMany(p => p.MstUserRolesConfigs)
                .HasForeignKey(d => d.RoleId)
                .HasConstraintName("FK__mstUserRo__RoleI__6C190EBB");

            entity.HasOne(d => d.User).WithMany(p => p.MstUserRolesConfigs)
                .HasForeignKey(d => d.UserId)
                .HasConstraintName("FK__mstUserRo__UserI__6B24EA82");
        });

        modelBuilder.Entity<MstUsersRole>(entity =>
        {
            entity.HasKey(e => e.RoleId).HasName("PK__mstUsers__8AFACE3A82206306");

            entity.ToTable("mstUsersRoles");

            entity.Property(e => e.RoleId).HasColumnName("RoleID");
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.RoleName)
                .HasMaxLength(512)
                .IsUnicode(false);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}

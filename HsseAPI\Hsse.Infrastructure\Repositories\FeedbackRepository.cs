using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class FeedbackRepository : IFeedbackRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public FeedbackRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

    }
}

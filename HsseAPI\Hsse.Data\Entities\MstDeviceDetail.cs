﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstDeviceDetail
{
    public int Id { get; set; }

    public int? FacilityId { get; set; }

    public string? Osname { get; set; }

    public string? AppVersion { get; set; }

    public DateTime? Datetime { get; set; }

    public string? Fcm { get; set; }

    public string? Location { get; set; }

    public string? DeviceUniqueId { get; set; }

    public string? Lat { get; set; }

    public string? Long { get; set; }

    public DateTime? CreatedAt { get; set; }

    public virtual MstFacility? Facility { get; set; }
}

﻿using Asp.Versioning;
using Hsse.Application.Helper;
using Hsse.Application.IServices;
using Hsse.Application.Services;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class LoginController : ControllerBase
    {
        private readonly ILoginService _ILoginService;
        private readonly ILogger<AnnouncementController> _logger;
        private readonly AuthorizeHelper _AuthorizeHelper;
        public LoginController(ILoginService loginService, AuthorizeHelper authorizeHelper, IConfiguration configuration, ILogger<AnnouncementController> logger)
        {
            _logger = logger;
            _ILoginService = loginService;
            _AuthorizeHelper = authorizeHelper;
        }
        
        [AllowAnonymous]
        [HttpPost("ValidateUser")]
        public IActionResult ValidateUser(string? employeeEmail = null, string? employeeId = null)
        {
            var response = new ResponseDetails();
            try
            {
                var result = _ILoginService.ValidateUser(employeeEmail, employeeId);
                if (result != null)
                {
                    response.Status = 1;
                    response.Message = "User validation completed successfully";
                    result.Token = _AuthorizeHelper.GenerateToken(result);  
                    response.Result = result;
                }
                else
                {
                    response.Status = 0;
                    response.Message = "User validation failed";
                    response.Result = null;
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing login operation";
                response.Result = null;
                return BadRequest(response);
            }
        }
    }
}

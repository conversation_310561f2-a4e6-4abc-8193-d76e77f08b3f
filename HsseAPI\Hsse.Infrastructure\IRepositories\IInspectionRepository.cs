﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IInspectionRepository
    {
        long CreateInspection(CreateInspectionDto createInspectionDto);
        int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto);
        int VerifyInspector(VerifyInspectorDto verifyInspectorDto);
        List<MstInspection> GetInspections();
        List<MstActionParty> GetActionParties();
    }
}

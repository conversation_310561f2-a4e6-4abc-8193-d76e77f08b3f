﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Helper
{
    public class EncryptionMiddleware
    {
        private readonly RequestDelegate _next;

        public EncryptionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, EncryptionHelper encryptionHelper)
        {
            // Read and decrypt request body
            if (context.Request.ContentLength > 0)
            {
                context.Request.EnableBuffering();
                using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, leaveOpen: true);
                var encryptedContent = await reader.ReadToEndAsync();
                context.Request.Body.Position = 0;

                if (!string.IsNullOrWhiteSpace(encryptedContent))
                {
                    var decrypted = encryptionHelper.Decrypt(encryptedContent);
                    var bytes = Encoding.UTF8.GetBytes(decrypted);
                    context.Request.Body = new MemoryStream(bytes);
                }
            }

            // Capture and encrypt response
            var originalBodyStream = context.Response.Body;
            using var memoryStream = new MemoryStream();
            context.Response.Body = memoryStream;

            await _next(context); // Proceed to next middleware

            memoryStream.Position = 0;
            using var reader2 = new StreamReader(memoryStream);
            var responseBody = await reader2.ReadToEndAsync();

            context.Response.Body = originalBodyStream;

            if (!string.IsNullOrWhiteSpace(responseBody))
            {
                var encryptedResponse = encryptionHelper.Encrypt(responseBody);
                await context.Response.WriteAsync(encryptedResponse);
            }
        }
    }

}

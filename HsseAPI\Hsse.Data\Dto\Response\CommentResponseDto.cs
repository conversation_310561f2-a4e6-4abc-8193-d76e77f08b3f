﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class CommentResponseDto
    {
        public int CommentID { get; set; }
        public int? ParentCommentID { get; set; }
        public int UserID { get; set; }
        public string? Name { get; set; }
        public string? CommentText { get; set; }
        public DateTime? CommentedAt { get; set; }
        public int LikesCount { get; set; }
        public List<CommentLikeDto> Likes { get; set; } = [];
    }
    public class CommentLikeDto
    {
        public int CommentLikeId { get; set; }

        public int? CommentId { get; set; }

        public int? UserId { get; set; }
        public string? Name { get; set; }
    }
}

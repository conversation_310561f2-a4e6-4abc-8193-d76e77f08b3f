using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class EventRepository : IEventRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public EventRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreateOrUpdateEvent(CreateEventDto createEventDto)
        {
            if (createEventDto.EventID == 0)
            {
                // Create new event
                var newEvent = new MstEvent
                {
                    Title = createEventDto.Title,
                    Description = createEventDto.Description,
                    MediaUrl = createEventDto.MediaURL,
                    EventDateTime = createEventDto.EventDateTime,
                    Location = createEventDto.Location,
                    ExternalLink = createEventDto.ExternalLink,
                    CreatedBy = createEventDto.CreatedBy,
                    CreatedAt = DateTime.Now,
                    FacilityId = createEventDto.FacilityID,
                    IsActive = createEventDto.IsActive,
                    ScheduleAt = createEventDto.ScheduleAt,
                    ExpiryAt = createEventDto.ExpiryAt,
                    IsRsvp = createEventDto.IsRsvp
                };

                _MasterDBContext.MstEvents.Add(newEvent);
                _MasterDBContext.SaveChanges();

                // Add receivers
                foreach (var userId in createEventDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceiver
                    {
                        EventId = newEvent.EventId,
                        UserId = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createEventDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceiver
                    {
                        EventId = newEvent.EventId,
                        GroupId = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                _MasterDBContext.SaveChanges();
                return newEvent.EventId;
            }
            else
            {
                // Update existing event
                var existingEvent = _MasterDBContext.MstEvents
                    .FirstOrDefault(e => e.EventId == createEventDto.EventID);

                if (existingEvent == null)
                    return 0;

                existingEvent.Title = createEventDto.Title;
                existingEvent.Description = createEventDto.Description;
                existingEvent.MediaUrl = createEventDto.MediaURL;
                existingEvent.EventDateTime = createEventDto.EventDateTime;
                existingEvent.Location = createEventDto.Location;
                existingEvent.ExternalLink = createEventDto.ExternalLink;
                existingEvent.FacilityId = createEventDto.FacilityID;
                existingEvent.IsActive = createEventDto.IsActive;
                existingEvent.ScheduleAt = createEventDto.ScheduleAt;
                existingEvent.ExpiryAt = createEventDto.ExpiryAt;
                existingEvent.IsRsvp = createEventDto.IsRsvp;

                // Remove existing receivers, then add new ones
                var existingReceivers = _MasterDBContext.MstAnnouncementReceivers
                    .Where(r => r.EventId == createEventDto.EventID);
                _MasterDBContext.MstAnnouncementReceivers.RemoveRange(existingReceivers);

                // Add new receivers
                foreach (var userId in createEventDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceiver
                    {
                        EventId = createEventDto.EventID,
                        UserId = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createEventDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceiver
                    {
                        EventId = createEventDto.EventID,
                        GroupId = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                _MasterDBContext.SaveChanges();
                return existingEvent.EventId;
            }
        }

        public List<MstEvent> GetEvents()
        {
            return _MasterDBContext.MstEvents.ToList();
        }
    }
}

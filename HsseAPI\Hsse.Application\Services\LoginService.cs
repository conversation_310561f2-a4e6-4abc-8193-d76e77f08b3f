﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Infrastructure.IRepositories;
using Hsse.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class LoginService : ILoginService
    {
        private readonly ILoginRepository _ILoginRepository;
        public LoginService(ILoginRepository loginRepository)
        {
            _ILoginRepository = loginRepository;
        }
        public LoginResponseDto ValidateUser(string? employeeEmail = null, string? employeeId = null)
        {
            var result = _ILoginRepository.ValidateUser(employeeEmail, employeeId);
            return result;
        }
    }
}

using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class EventController : ControllerBase
    {
        private readonly IEventService _IEventService;
        private readonly ILogger<EventController> _logger;

        public EventController(IEventService eventService, IConfiguration configuration, ILogger<EventController> logger)
        {
            _logger = logger;
            _IEventService = eventService;
        }

        [HttpPost("CreateOrUpdateEvent")]
        public IActionResult CreateOrUpdateEvent([FromBody] CreateEventDto createEventDto)
        {
            var response = new ResponseDetails();
            try
            {
                long result = _IEventService.CreateOrUpdateEvent(createEventDto);
                response.Status = 1;
                response.Message = "Event operation completed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing event operation";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [Authorize]
        [HttpGet("GetEvents")]
        public IActionResult GetEvents()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IEventService.GetEvents();
                response.Status = 1;
                response.Message = "Events retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving events";
                response.Result = null;
                return BadRequest(response);
            }
        }
    }
}

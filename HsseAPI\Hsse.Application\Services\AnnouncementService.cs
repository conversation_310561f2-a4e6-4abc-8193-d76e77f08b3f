﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class AnnouncementService : IAnnouncementService
    {
        private readonly IAnnouncementRepository _IAnnouncementRepository;

        public AnnouncementService(IAnnouncementRepository announcementRepository)
        {
            _IAnnouncementRepository = announcementRepository;
        }

        public long CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto)
        {
            var result = _IAnnouncementRepository.CreateOrUpdateAnnouncement(createAnnouncementDto);
            return result;
        }

        public int CreateOrUpdateCategory(CreateAnnouncementCategoryDto createCategoryDto)
        {
            var result = _IAnnouncementRepository.CreateOrUpdateCategory(createCategoryDto);
            return result;
        }

        public List<MstAnnouncementCategory> GetCategories()
        {
            var result = _IAnnouncementRepository.GetCategories();
            return result;
        }

        public List<MstAnnouncement> GetAnnouncements()
        {
            var result = _IAnnouncementRepository.GetAnnouncements();
            return result;
        }

    }
}

using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class EventService : IEventService
    {
        private readonly IEventRepository _IEventRepository;

        public EventService(IEventRepository eventRepository)
        {
            _IEventRepository = eventRepository;
        }

        public long CreateOrUpdateEvent(CreateEventDto createEventDto)
        {
            var result = _IEventRepository.CreateOrUpdateEvent(createEventDto);
            return result;
        }


        public List<MstEvent> GetEvents()
        {
            var result = _IEventRepository.GetEvents();
            return result;
        }
    }
}

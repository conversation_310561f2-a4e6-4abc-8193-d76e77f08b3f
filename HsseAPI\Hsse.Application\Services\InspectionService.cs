﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class InspectionService : IInspectionService
    {
        private readonly IInspectionRepository _IInspectionRepository;

        public InspectionService(IInspectionRepository inspectionRepository)
        {
            _IInspectionRepository = inspectionRepository;
        }

        public long CreateInspection(CreateInspectionDto createInspectionDto)
        {
            var result = _IInspectionRepository.CreateInspection(createInspectionDto);
            return result;
        }

        public int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto)
        {
            var result = _IInspectionRepository.VerifyActionParty(verifyActionPartyDto);
            return result;
        }

        public int VerifyInspector(VerifyInspectorDto verifyInspectorDto)
        {
            var result = _IInspectionRepository.VerifyInspector(verifyInspectorDto);
            return result;
        }

        public List<MstInspection> GetInspections()
        {
            var result = _IInspectionRepository.GetInspections();
            return result;
        }

        public List<MstActionParty> GetActionParties()
        {
            var result = _IInspectionRepository.GetActionParties();
            return result;
        }

    }
}

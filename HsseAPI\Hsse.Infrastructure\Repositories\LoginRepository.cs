﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class LoginRepository : ILoginRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;


        public LoginRepository(IConfiguration configuration, HttpClient httpClient, MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
            _httpClient = httpClient;
            _configuration = configuration;
        }

        public LoginResponseDto ValidateUser(string? employeeEmail = null, string? employeeId = null)
        {
            try
            {
                var user = _MasterDBContext.MstUsers
                    .Where(u => u.Email == employeeEmail || u.EmployeeCode == employeeId)
                    .FirstOrDefault();

                if (user != null)
                {
                    var userRoleConfig = _MasterDBContext.MstUserRolesConfigs
                        .Where(x => x.UserId == user.UserId)
                        .FirstOrDefault();

                    var roleName = _MasterDBContext.MstUsersRoles
                        .Where(x => x.RoleId == userRoleConfig.RoleId)
                        .Select(x => x.RoleName)
                        .FirstOrDefault();

                    var facilityName = _MasterDBContext.MstFacilities
                        .Where(x => x.FacilityId == userRoleConfig.FacilityId)
                        .Select(x => x.FacilityName)
                        .FirstOrDefault();

                    var result = new LoginResponseDto
                    {
                        UserId = user.UserId,
                        Name = $"{user.FirstName} {user.LastName}",
                        EmployeeId = user.EmployeeCode,
                        RoleId = userRoleConfig.RoleId,
                        RoleName = roleName,
                        facilityId = userRoleConfig.FacilityId,
                        FacilityName = facilityName,
                        ProfileImageUrl = user.ProfileImageUrl,
                        LastLogin = user.LastLogin?.ToString("yyyy-MM-dd HH:mm:ss"),
                        Email = user.Email,
                    };
                    return result;
                }
                return null;

            }
            catch (Exception ex)
            {
                throw ex;
            }

        }
    }
}


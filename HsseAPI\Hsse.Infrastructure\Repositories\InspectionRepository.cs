﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class InspectionRepository : IInspectionRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public InspectionRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreateInspection(CreateInspectionDto createInspectionDto)
        {
            if (createInspectionDto.InspectionId == 0)
            {
                // Create new inspection
                var newInspection = new MstInspection
                {
                    FacilityId = createInspectionDto.FacilityID,
                    Title = createInspectionDto.Title,
                    Description = createInspectionDto.Description,
                    InspectionDate = createInspectionDto.InspectionDate,
                    CreatedBy = createInspectionDto.CreatedBy,
                    CreatedAt = DateTime.Now,
                    ReferenceNo = createInspectionDto.ReferenceNo
                };

                _MasterDBContext.MstInspections.Add(newInspection);
                _MasterDBContext.SaveChanges();

                // Add inspection items
                foreach (var item in createInspectionDto.InspectionItems)
                {
                    var inspectionItem = new MstInspectionItem
                    {
                        InspectionId = newInspection.InspectionId,
                        Description = item.Description,
                        SpecificLocation = item.SpecificLocation,
                        Recommendation = item.Recommendation,
                        ActionPartyId = item.ActionPartyName,
                        Status = item.Status,
                        Rectification = item.Rectification,
                        AfterImagePath = item.AfterImagePath,
                        CompletionDateTime = item.CompletionDateTime,
                        CreatedAt = DateTime.Now,
                        RecommendationMediaUrl = item.RecommendationMediaUrl,
                        Observation = item.Observation,
                        ObservationMediaUrl = item.ObservationMediaUrl,
                        Verification = item.Verification
                    };

                    _MasterDBContext.MstInspectionItems.Add(inspectionItem);
                }

                _MasterDBContext.SaveChanges();
                return newInspection.InspectionId;
            }
            else
            {
                // Update existing inspection
                var existingInspection = _MasterDBContext.MstInspections
                    .FirstOrDefault(i => i.InspectionId == createInspectionDto.InspectionId);

                if (existingInspection == null)
                    return 0;

                existingInspection.FacilityId = createInspectionDto.FacilityID;
                existingInspection.Title = createInspectionDto.Title;
                existingInspection.Description = createInspectionDto.Description;
                existingInspection.InspectionDate = createInspectionDto.InspectionDate;
                existingInspection.ModifiedAt = DateTime.Now;
                existingInspection.ReferenceNo = createInspectionDto.ReferenceNo;

                // Remove existing items and add new ones
                var existingItems = _MasterDBContext.MstInspectionItems
                    .Where(i => i.InspectionId == createInspectionDto.InspectionId);
                _MasterDBContext.MstInspectionItems.RemoveRange(existingItems);

                // Add new items
                foreach (var item in createInspectionDto.InspectionItems)
                {
                    var inspectionItem = new MstInspectionItem
                    {
                        InspectionId = createInspectionDto.InspectionId,
                        Description = item.Description,
                        SpecificLocation = item.SpecificLocation,
                        Recommendation = item.Recommendation,
                        ActionPartyId = item.ActionPartyName,
                        Status = item.Status,
                        Rectification = item.Rectification,
                        AfterImagePath = item.AfterImagePath,
                        CompletionDateTime = item.CompletionDateTime,
                        CreatedAt = DateTime.Now,
                        ModifiedAt = DateTime.Now,
                        RecommendationMediaUrl = item.RecommendationMediaUrl,
                        Observation = item.Observation,
                        ObservationMediaUrl = item.ObservationMediaUrl,
                        Verification = item.Verification
                    };

                    _MasterDBContext.MstInspectionItems.Add(inspectionItem);
                }

                _MasterDBContext.SaveChanges();
                return existingInspection.InspectionId;
            }
        }

        public int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto)
        {
            var inspectionItem = _MasterDBContext.MstInspectionItems
                .FirstOrDefault(i => i.ItemId == verifyActionPartyDto.ItemId);

            if (inspectionItem == null)
                return 0; // Item not found

            // Store old values for audit
            var oldActionParty = inspectionItem.ActionPartyId?.ToString();
            var oldVerification = inspectionItem.Verification?.ToString();

            inspectionItem.ActionPartyId = verifyActionPartyDto.ActionPartyId.ToString();
            inspectionItem.Verification = 1; // Assuming 1 means verified
            inspectionItem.ModifiedAt = DateTime.Now;

            // Add audit log using the correct entity structure
            var auditLog = new MstInspectionItemAudit
            {
                ItemId = verifyActionPartyDto.ItemId,
                OldStatus = oldVerification,
                NewStatus = "1", // Verified
                ChangedBy = verifyActionPartyDto.VerifiedBy,
                ChangedAt = DateTime.Now
            };

            _MasterDBContext.MstInspectionItemAudits.Add(auditLog);
            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int VerifyInspector(VerifyInspectorDto verifyInspectorDto)
        {
            var inspection = _MasterDBContext.MstInspections
                .FirstOrDefault(i => i.InspectionId == verifyInspectorDto.InspectionId);

            if (inspection == null)
                return 0; // Inspection not found

            // For inspector verification, we'll use the general audit log table
            // since MstInspectionItemAudit is specifically for inspection items
            var auditLog = new MstAuditLog
            {
                TableName = "MstInspections",
                RecordPrimaryKey = verifyInspectorDto.InspectionId,
                OperationType = "Inspector Verification",
                OldValues = $"Inspector: Not Verified",
                NewValues = $"Inspector: {verifyInspectorDto.InspectorId}, Verified: {verifyInspectorDto.IsVerified}",
                ChangedBy = verifyInspectorDto.InspectorId,
                ChangedAt = DateTime.Now
            };

            _MasterDBContext.MstAuditLogs.Add(auditLog);
            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public List<MstInspection> GetInspections()
        {
            return _MasterDBContext.MstInspections.ToList();
        }

        public List<MstActionParty> GetActionParties()
        {
            return _MasterDBContext.MstActionParties.ToList();
        }
    }
}
